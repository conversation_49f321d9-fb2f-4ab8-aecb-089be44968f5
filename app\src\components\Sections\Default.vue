<script setup lang="ts">
import type { DefaultOptions } from "./types";

const props = defineProps<{
	options: DefaultOptions;
}>();
</script>

<template>
  <div class="flex flex-col gap-4 w-full py-8 items-center">
    <div class="flex flex-col w-[85%] bg-gray-400/10 justify-center items-center p-8 py-16 rounded-lg">
      <div class="flex flex-col gap-4 w-4/5">
        <h2 class="header">
          {{ props.options.title }}
        </h2>

        <div class="flex flex-wrap gap-4">
          <div v-html="props.options.content" />
        </div>
      </div>
    </div>
  </div>

</template>
