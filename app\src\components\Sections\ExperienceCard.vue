<script setup lang="ts">
import SkillBox from "../SkillBox.vue";
import type { CardsOptions } from "./types";

const props = defineProps<{
	options: CardsOptions;
}>();
</script>

<template>
  <div class="flex flex-col gap-4 w-3/5 py-8">
    <h2
      class="header"
    >
      {{ props.options.title }}
    </h2>

    <div class="flex flex-col flex-wrap gap-4 w-full">
      <div v-html="props.options.content"></div>
      <div class="flex flex-wrap gap-4">
        <SkillBox v-for="card in props.options.experienceCards" :key="card.id" :url="card.url">
          <template #title>{{ card.title }}</template>
            <template #description>
              <div v-html="card.description"></div>
            </template>
            <template #button>{{ card.urlText }}</template>
        </SkillBox>
      </div>
    </div>
  </div>
</template>
