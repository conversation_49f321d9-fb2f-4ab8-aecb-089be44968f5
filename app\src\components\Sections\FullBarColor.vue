<script setup lang="ts">
import type { PropType } from "vue";

interface IOptions {
	title: string;
	color: string;
	content: string;
}

defineProps({
	options: {
		type: Object as PropType<IOptions>,
		required: true,
	},
});
</script>


<template>
    <div :class="[options.color, 'flex flex-col items-center gap-4 w-full py-8']">
        <div class="w-3/5 flex flex-col gap-4">
            <div>
                <h2 class="header">
                    {{ options.title }}
                </h2>
            </div>
            <div class="flex flex-wrap gap-4">
                <div v-html="options.content" />
            </div>
        </div>
    </div>
</template>