{"collectionName": "components_web_content_parts_cards", "info": {"displayName": "Cards", "icon": "dashboard", "description": ""}, "options": {}, "attributes": {"title": {"type": "string", "required": true}, "experienceCards": {"type": "component", "repeatable": true, "component": "web-content-components.experience-card"}, "content": {"type": "customField", "options": {"preset": "defaultHtml"}, "customField": "plugin::ckeditor5.CKEditor"}}}