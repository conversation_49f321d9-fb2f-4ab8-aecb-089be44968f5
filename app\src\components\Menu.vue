<template>
  <div ref="wrapper" class="fixed top-14 left-1/2 -translate-x-1/2 z-50 w-fit rounded-full p-[1px] overflow-hidden">
    <div
      class="pointer-events-none absolute inset-0 before:absolute before:inset-0">
    </div>
    <nav :class="navClasses"
      class="relative z-10 flex h-8 items-center gap-4 px-8 rounded-full text-sm font-medium transition-colors duration-150">
      <a href="/" :class="hover:opacity-80 transition">Domů</a>
      <a href="/test" class="hover:opacity-80 transition">Test</a>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';

const ctx = document.createElement('canvas').getContext('2d')!;
const wrapper = ref<HTMLElement | null>(null);
const lightBg = ref(false);
let rafId = 0;

function toRGB(col: string): [number, number, number] {
  try {
    ctx.fillStyle = '#000'; ctx.fillStyle = col;
    const nums = ctx.fillStyle.match(/\d+/g)?.map(Number);
    if (nums && nums.length >= 3) return [nums[0], nums[1], nums[2]];
  } catch { }
  return [255, 255, 255];
}
function isLight(col: string): boolean {
  const [r, g, b] = toRGB(col);
  return 0.299 * r + 0.587 * g + 0.114 * b > 140;
}
function effectiveBg(el: HTMLElement | null): string {
  while (el) {
    const bg = getComputedStyle(el).backgroundColor;
    if (bg && !/rgba?\([^)]+,\s*0\)/.test(bg) && bg !== 'transparent') return bg;
    el = el.parentElement;
  }
  return 'rgb(255 255 255)';
}
function themeClass(el: HTMLElement | null): 'light' | 'dark' | null {
  while (el) {
    if (el.classList.contains('light')) return 'light';
    if (el.classList.contains('dark')) return 'dark';
    el = el.parentElement;
  }
  return null;
}

function sample() {
  const w = wrapper.value;
  if (!w) { rafId = requestAnimationFrame(sample); return; }
  const { left, top, width, height } = w.getBoundingClientRect();
  const cx = left + width / 2, cy = top + height / 2;
  w.style.visibility = 'hidden';
  const under = document.elementFromPoint(cx, cy) as HTMLElement | null;
  const theme = themeClass(under);
  lightBg.value = theme === 'light' ? true :
    theme === 'dark' ? false :
      isLight(effectiveBg(under));
  w.style.visibility = '';
  rafId = requestAnimationFrame(sample);
}

onMounted(() => { rafId = requestAnimationFrame(sample); });
onUnmounted(() => cancelAnimationFrame(rafId));

const navClasses = computed(() =>
  lightBg.value
    ? 'bg-black/10 text-black mix-blend-difference border border-black/20 backdrop-blur-xl backdrop-brightness-110'
    : 'bg-white/20 text-white mix-blend-difference border border-white/25 backdrop-blur-xl backdrop-brightness-95'
);
</script>

<style scoped></style>
