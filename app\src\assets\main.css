@import 'tailwindcss';     /* pulls in core framework  */
@import "tailwindcss-intersect";

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
  
*/

@theme {
  --animate-slow-sway: slow-sway 1s ease-in-out infinite;

  @keyframes slow-sway {
    0%,100% { transform: translateX(-8%) translateY(-8%); }
    50%      { transform: translateX(8%)  translateY(8%);  }
  }
}


@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer components {
  * {
    @apply scroll-smooth scroll-m-8;
  }
  code {
    @apply bg-neutral-950/30 px-2 rounded-sm;
  }
}

.header {
  @apply w-fit pl-1 pr-12 pt-2 pb-0.5 text-2xl text-black border-b-1 border-black/20;
}

