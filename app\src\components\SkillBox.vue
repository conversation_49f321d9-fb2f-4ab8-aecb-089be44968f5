<script setup lang="ts">
const props = defineProps<{
	url: string;
}>();
</script>

<template>
    <div class="flex gap-4 min-w-80 flex-col flex-1 px-6 py-6 rounded-lg bg-neutral-700 shadow-lg text-white">
        <div>
            <h3 class="text-lg">
                <slot name="title"></slot>
            </h3>
        </div>
        <div class="w-full bg-amber-500 h-0.5">

        </div>
        <div class="text-white/90 flex-1">
            <p>
                <slot name="description"></slot>
            </p>
        </div>

        <a class="cursor-pointer text-amber-300 self-end hover:text-amber-200" :href="props.url">
            <slot name="button">
                Learn More
            </slot>
        </a>
    </div>
</template>