import { defineS<PERSON> } from "pinia";
import { ref } from "vue";
import { treaty } from "@elysiajs/eden";
import type { App } from "./../../../server/src/index";


interface IPageContent {
	error?: string;
	data?: {
		id: number;
		documentId: string;
		slug: string;
		title: string;
		locale: string;
		content: any[];
	};
}

const client = treaty<App>("localhost:3000");

export const usePageContentStore = defineStore("pageContent", () => {
	const pageContent = ref<IPageContent>({ error: undefined, data: undefined });

	const fetchPageContent = async (slug: string) => {
		const r = await client.strapi["web-content"].get({ query: { slug: slug } });

		if (r.data === null || (r.data && "error" in r.data)) {
			pageContent.value = {
				error: r.data && "error" in r.data ? r.data.error : "Unknown error",
				data: undefined,
			};
			return;
		}

		pageContent.value.data = r.data;
	};

	return {
		pageContent,
		fetchPageContent,
	};
});
