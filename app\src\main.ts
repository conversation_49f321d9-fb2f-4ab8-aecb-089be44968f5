import "./assets/main.css";
import { createApp } from "vue";
import { createPinia } from "pinia";

import App from "./App.vue";
import router from "./router";
import { Observer } from "tailwindcss-intersect";

const app = createApp(App);

app.use(createPinia());
app.use(router);

app.mount("#app");

Observer.start();

router.afterEach(() => {
	Observer.restart(); // scan the new DOM and re-observe everything
});
