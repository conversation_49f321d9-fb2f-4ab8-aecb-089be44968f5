<script setup lang="ts">
import { computed } from "vue";
import type { HeaderOptions } from "./types";

const props = defineProps<{
  options: HeaderOptions;
}>();

const coverImage = computed(
  () => `url(http://localhost:1337${props.options.coverImage.url})`,
);
</script>

<template>
  <div
    class="flex flex-col gap-4 h-[calc(100vh-2.5rem-2rem)] w-full items-center justify-center dark bg-image-custom mb-8 shadow relative">
    <!-- social links, bottom‑right -->
    <div class="absolute right-8 bottom-8 flex gap-4">

      <!-- Instagram -->
      <div class="relative p-[1px] rounded-full overflow-hidden">
        <!-- animated highlight -->
        <div class="pointer-events-none absolute inset-0
                before:absolute before:inset-0
                before:bg-[radial-gradient(circle_at_40%_40%,rgba(255,255,255,0.35)_0%,transparent_60%)]
                before:animate-[slow-sway_8s_linear_infinite]"></div>

        <a href="https://instagram.com" class="relative z-10 flex items-center px-4 py-1 rounded-full
              bg-white/10 backdrop-blur-md
              border border-white/25 shadow-[0_4px_10px_rgba(0,0,0,0.25)]
              text-white/90 hover:text-white">
          Instagram
        </a>
      </div>

      <!-- GitHub -->
      <div class="relative p-[1px] rounded-full overflow-hidden">
        <div class="pointer-events-none absolute inset-0
                before:absolute before:inset-0
                before:bg-[radial-gradient(circle_at_40%_40%,rgba(255,255,255,0.35)_0%,transparent_60%)]
                before:animate-[slow-sway_8s_linear_infinite]"></div>

        <a href="https://github.com" class="relative z-10 flex items-center px-4 py-1 rounded-full
              bg-white/10 backdrop-blur-md
              border border-white/25 shadow-[0_4px_10px_rgba(0,0,0,0.25)]
              text-white/90 hover:text-white">
          GitHub
        </a>
      </div>

      <!-- LinkedIn -->
      <div class="relative p-[1px] rounded-full overflow-hidden">
        <div class="pointer-events-none absolute inset-0
                before:absolute before:inset-0
                before:bg-[radial-gradient(circle_at_40%_40%,rgba(255,255,255,0.35)_0%,transparent_60%)]
                before:animate-[slow-sway_8s_linear_infinite]"></div>

        <a href="https://linkedin.com" class="relative z-10 flex items-center px-4 py-1 rounded-full
              bg-white/10 backdrop-blur-md
              border border-white/25 shadow-[0_4px_10px_rgba(0,0,0,0.25)]
              text-white/90 hover:text-white">
          LinkedIn
        </a>
      </div>

    </div>

    <div class="flex flex-col w-fit">
      <div class="flex flex-col w-fit">
        <div class="self-end text-neutral-100 text-lg">I am</div>
        <div class="bg-amber-500 text-white pr-4 pl-12 py-2 text-2xl w-fit">
          Vojta
        </div>
      </div>
      <div class="bg-amber-600 text-white ml-12 pl-4 pr-12 py-2 text-2xl">
        UX designer <br /> &amp; Fullstack developer
      </div>
    </div>

    <div class="w-2/5 text-white" v-html="props.options.content" />

    <a href="#experiences" class="px-8 py-2 mt-8 cursor-pointer rounded-lg shadow-sm bg-amber-500
                    text-lg text-white">
      Get To Know More
    </a>
  </div>
</template>

<style scoped>
.bg-image-custom {
  background-image: linear-gradient(to bottom left,
      rgba(120, 53, 15, 0.7),
      /* amber‑900/40 */
      rgba(251, 146, 60, 0.5)
      /* amber‑600/50 */
    ),
    v-bind(coverImage);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
</style>